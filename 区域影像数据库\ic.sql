/*
 Navicat Premium Data Transfer

 Source Server         : postgres
 Source Server Type    : PostgreSQL
 Source Server Version : 160009
 Source Host           : localhost:5432
 Source Catalog        : postgres
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 160009
 File Encoding         : 65001

 Date: 01/08/2025 16:14:16
*/


-- ----------------------------
-- Table structure for t_download_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_download_record";
CREATE TABLE "public"."t_download_record" (
  "record_id" "pg_catalog"."numeric" NOT NULL,
  "is_deleted" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "create_time" "pg_catalog"."timestamp" NOT NULL,
  "create_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "update_time" "pg_catalog"."timestamp" NOT NULL,
  "update_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "study_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "system_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "annet_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "task_type" "pg_catalog"."int2" NOT NULL,
  "task_field" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "download_fail_reason" "pg_catalog"."text" COLLATE "pg_catalog"."default" NOT NULL,
  "download_begin_time" "pg_catalog"."timestamp" NOT NULL,
  "download_end_time" "pg_catalog"."timestamp" NOT NULL,
  "download_spend_time" "pg_catalog"."numeric" NOT NULL,
  "ic_id" "pg_catalog"."numeric" NOT NULL,
  "ic_ip" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "node_id" "pg_catalog"."numeric" NOT NULL,
  "node_type" "pg_catalog"."int2" NOT NULL,
  "node_file_path" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "task_id" "pg_catalog"."numeric"
)
;
COMMENT ON COLUMN "public"."t_download_record"."record_id" IS '记录id';
COMMENT ON COLUMN "public"."t_download_record"."is_deleted" IS '是否删除: 1.删除 0.未删除';
COMMENT ON COLUMN "public"."t_download_record"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_download_record"."create_user" IS '创建者';
COMMENT ON COLUMN "public"."t_download_record"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_download_record"."update_user" IS '更新者';
COMMENT ON COLUMN "public"."t_download_record"."org_code" IS '机构编码';
COMMENT ON COLUMN "public"."t_download_record"."org_name" IS '机构名称';
COMMENT ON COLUMN "public"."t_download_record"."study_id" IS '检查号';
COMMENT ON COLUMN "public"."t_download_record"."system_id" IS '信息系统在医院的唯一ID，该字段用来解决同一家医院不同系统中RIS_STUDY_ID相同的问题。建议从pacs01开始';
COMMENT ON COLUMN "public"."t_download_record"."annet_id" IS '在平台中检查唯一标识符。';
COMMENT ON COLUMN "public"."t_download_record"."task_type" IS '任务类型：0.FTP，1.QR，2.LAN 3.S3 4.SFTP 5.LOCAL 6.HTTP/HTTPS 7.QR_PASSIVE';
COMMENT ON COLUMN "public"."t_download_record"."task_field" IS '任务属性：0.图像 1.报告快照 2.图文报告 3.申请单';
COMMENT ON COLUMN "public"."t_download_record"."download_fail_reason" IS '失败原因';
COMMENT ON COLUMN "public"."t_download_record"."download_begin_time" IS '下载开始时间';
COMMENT ON COLUMN "public"."t_download_record"."download_end_time" IS '下载结束时间';
COMMENT ON COLUMN "public"."t_download_record"."download_spend_time" IS '下载耗时（单位：秒）';
COMMENT ON COLUMN "public"."t_download_record"."ic_id" IS '影像中心id';
COMMENT ON COLUMN "public"."t_download_record"."ic_ip" IS '影像中心ip';
COMMENT ON COLUMN "public"."t_download_record"."node_id" IS '节点id';
COMMENT ON COLUMN "public"."t_download_record"."node_type" IS '节点类型:0.本地 1.FTP 2.LAN 3.S3 4.Feign 5.HTTP';
COMMENT ON COLUMN "public"."t_download_record"."node_file_path" IS '节点路径';
COMMENT ON TABLE "public"."t_download_record" IS '下载记录表';

-- ----------------------------
-- Table structure for t_ic_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_ic_config";
CREATE TABLE "public"."t_ic_config" (
  "ic_id" "pg_catalog"."numeric" NOT NULL,
  "is_deleted" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "create_time" "pg_catalog"."timestamp" NOT NULL,
  "create_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "update_time" "pg_catalog"."timestamp" NOT NULL,
  "update_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "ic_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "sort" "pg_catalog"."int8" NOT NULL DEFAULT 1,
  "ic_ip" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "task_type" "pg_catalog"."int2" NOT NULL DEFAULT 1,
  "download_timeout" "pg_catalog"."int8" NOT NULL DEFAULT 3,
  "download_thread" "pg_catalog"."int8" NOT NULL DEFAULT 4,
  "check_type" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "send_process_interval" "pg_catalog"."int8" NOT NULL DEFAULT 5,
  "is_jpg" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "is_jpg_convert_dcm" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "is_open" "pg_catalog"."int2" NOT NULL DEFAULT 1,
  "qr_import_url" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "dicom_compress" "pg_catalog"."int2" NOT NULL DEFAULT 1,
  "ic_hearbeat_status" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "heartbeat_last_date_time" "pg_catalog"."timestamp",
  "ic_version" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT '未知'::character varying,
  "ic_server_version" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT '未知'::character varying,
  "cur_download_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT '无'::character varying,
  "dicom_tag_map" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "qr_passive_study_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "qr_passive_study_id_filter" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "qr_passive_system_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "qr_passive_file_path_format" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "download_max_fail" "pg_catalog"."int8" NOT NULL DEFAULT '-1'::integer,
  "fail_cache_dir" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "default_node" "pg_catalog"."numeric",
  "download_time_config" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT '[]'::character varying,
  "exclude_file_suffix" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "qr_local_ae" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "qr_listen_port" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "qr_server_ip" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "qr_server_port" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "qr_server_ae" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "ftp_path" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "lan_path" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "mapping_disk" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "connect_host" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "connect_user_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "connect_password" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "connect_port" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "ftp_mode" "pg_catalog"."int2",
  "front_days" "pg_catalog"."int4" DEFAULT 0,
  "task_interval_time" "pg_catalog"."int4" DEFAULT 0,
  "StudioCity" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "StudioCountry" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "HospitalMemo" "pg_catalog"."int2",
  "HospitalType" "pg_catalog"."int2"
)
;
COMMENT ON COLUMN "public"."t_ic_config"."ic_id" IS '影像中心id';
COMMENT ON COLUMN "public"."t_ic_config"."is_deleted" IS '是否删除: 1.删除 0.未删除';
COMMENT ON COLUMN "public"."t_ic_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_ic_config"."create_user" IS '创建者';
COMMENT ON COLUMN "public"."t_ic_config"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_ic_config"."update_user" IS '更新者';
COMMENT ON COLUMN "public"."t_ic_config"."org_code" IS '机构编码';
COMMENT ON COLUMN "public"."t_ic_config"."org_name" IS '机构名称';
COMMENT ON COLUMN "public"."t_ic_config"."ic_name" IS '影像中心名';
COMMENT ON COLUMN "public"."t_ic_config"."sort" IS '排序:大前小后';
COMMENT ON COLUMN "public"."t_ic_config"."ic_ip" IS '影像中心ip';
COMMENT ON COLUMN "public"."t_ic_config"."task_type" IS '任务类型：0.FTP 1.QR 2.LAN 3.S3 4.SFTP 5.LOCAL 6.HTTP/HTTPS 7.QR_PASSIVE';
COMMENT ON COLUMN "public"."t_ic_config"."download_timeout" IS '下载超时时间(级别:0~8):5m、10m、15m、20m、25m、30m、35m、40m、1h';
COMMENT ON COLUMN "public"."t_ic_config"."download_thread" IS '下载线程数(1~10)';
COMMENT ON COLUMN "public"."t_ic_config"."check_type" IS '检查系统类型：0放射类、1超声类、2内镜类、3核医学类、4病理类、5介入类、6心电类、7口腔类、8.眼科类 9.肺功能类 10.睡眠呼吸';
COMMENT ON COLUMN "public"."t_ic_config"."send_process_interval" IS '发送进度间隔(单位:秒)';
COMMENT ON COLUMN "public"."t_ic_config"."is_jpg" IS '是否生成jpg: 0.否 1.是';
COMMENT ON COLUMN "public"."t_ic_config"."is_jpg_convert_dcm" IS 'jpg转dcm: 0.否 1.是';
COMMENT ON COLUMN "public"."t_ic_config"."is_open" IS '是否开启';
COMMENT ON COLUMN "public"."t_ic_config"."qr_import_url" IS 'QR服务同步接口';
COMMENT ON COLUMN "public"."t_ic_config"."dicom_compress" IS 'dicom压缩：0.原图 1.JPEG_2000_Lossless 2.JPEG_Lossless';
COMMENT ON COLUMN "public"."t_ic_config"."ic_hearbeat_status" IS '心跳状态：0.DOWN 1.UP';
COMMENT ON COLUMN "public"."t_ic_config"."heartbeat_last_date_time" IS '最后一次心跳时间';
COMMENT ON COLUMN "public"."t_ic_config"."ic_version" IS '采集服务版本号';
COMMENT ON COLUMN "public"."t_ic_config"."ic_server_version" IS '服务端版本号';
COMMENT ON COLUMN "public"."t_ic_config"."cur_download_id" IS '当前下载ID，每次采集都会分配新的ID';
COMMENT ON COLUMN "public"."t_ic_config"."dicom_tag_map" IS 'dicom标签映射，多个用逗号隔开，格式:Tag=VR=Column。修改dicom里的tag值';
COMMENT ON COLUMN "public"."t_ic_config"."qr_passive_study_id" IS 'qr被动接收：检查流水号。这里填入Tag值';
COMMENT ON COLUMN "public"."t_ic_config"."qr_passive_study_id_filter" IS 'qr被动接收：studyId过滤';
COMMENT ON COLUMN "public"."t_ic_config"."qr_passive_system_id" IS 'qr被动接收，系统id';
COMMENT ON COLUMN "public"."t_ic_config"."qr_passive_file_path_format" IS 'qr被动接收，文件路径规则。填写Tag值或字符串，例如:pet/{Modality}/{StudyDate}/{StudyID}/{SeriesInstanceUID}/{SOPInstanceUID}-{InstanceNumber}.dcm';
COMMENT ON COLUMN "public"."t_ic_config"."download_max_fail" IS '采集最大失败次数：-1.无限制';
COMMENT ON COLUMN "public"."t_ic_config"."fail_cache_dir" IS '采集失败缓存目录';
COMMENT ON COLUMN "public"."t_ic_config"."default_node" IS '默认节点id';
COMMENT ON COLUMN "public"."t_ic_config"."download_time_config" IS '采集时间段JSON数组';
COMMENT ON COLUMN "public"."t_ic_config"."exclude_file_suffix" IS '过滤文件后缀名，扩展名不带“.”，多个以逗号分隔';
COMMENT ON TABLE "public"."t_ic_config" IS '采集服务表';

-- ----------------------------
-- Table structure for t_process
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_process";
CREATE TABLE "public"."t_process" (
  "process_id" "pg_catalog"."numeric" NOT NULL,
  "is_deleted" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "create_time" "pg_catalog"."timestamp" NOT NULL,
  "create_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "update_time" "pg_catalog"."timestamp" NOT NULL,
  "update_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "study_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "system_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "annet_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "check_date_time" "pg_catalog"."timestamp" NOT NULL,
  "process_type" "pg_catalog"."int8" NOT NULL,
  "process_spend" "pg_catalog"."int8" NOT NULL,
  "process_progress" "pg_catalog"."int8" NOT NULL DEFAULT 0,
  "code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "fail_count" "pg_catalog"."int8" NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "public"."t_process"."process_id" IS '流程id';
COMMENT ON COLUMN "public"."t_process"."is_deleted" IS '是否删除: 1.删除 0.未删除';
COMMENT ON COLUMN "public"."t_process"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_process"."create_user" IS '创建者';
COMMENT ON COLUMN "public"."t_process"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_process"."update_user" IS '更新者';
COMMENT ON COLUMN "public"."t_process"."org_code" IS '机构编码';
COMMENT ON COLUMN "public"."t_process"."study_id" IS '检查号';
COMMENT ON COLUMN "public"."t_process"."system_id" IS '信息系统在医院的唯一ID，该字段用来解决同一家医院不同系统中RIS_STUDY_ID相同的问题。建议从pacs01开始';
COMMENT ON COLUMN "public"."t_process"."annet_id" IS '在平台中检查唯一标识符';
COMMENT ON COLUMN "public"."t_process"."check_date_time" IS '检查时间';
COMMENT ON COLUMN "public"."t_process"."process_type" IS '进度类型：0.下载 1.序列 2.其他 3.插队 4.数量';
COMMENT ON COLUMN "public"."t_process"."process_spend" IS '预计耗时(单位：秒)';
COMMENT ON COLUMN "public"."t_process"."process_progress" IS '处理进度（单位：百分比）';
COMMENT ON COLUMN "public"."t_process"."code" IS '编码';
COMMENT ON COLUMN "public"."t_process"."remark" IS '备注';
COMMENT ON COLUMN "public"."t_process"."fail_count" IS '失败次数';
COMMENT ON TABLE "public"."t_process" IS '进度表';

-- ----------------------------
-- Table structure for t_report
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_report";
CREATE TABLE "public"."t_report" (
  "id" "pg_catalog"."numeric" NOT NULL,
  "is_deleted" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "create_time" "pg_catalog"."timestamp" NOT NULL,
  "create_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "update_time" "pg_catalog"."timestamp" NOT NULL,
  "update_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "org_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "study_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "system_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "annet_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "report_uid" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "check_type" "pg_catalog"."int2",
  "ris_study_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "is_disable" "pg_catalog"."int2" DEFAULT 0,
  "patient_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_no" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_serial_no" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_case_history_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_id_card_type" "pg_catalog"."int2" DEFAULT 6,
  "patient_id_card" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "patient_pinyin" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_gender" "pg_catalog"."int2",
  "patient_age" "pg_catalog"."int8" DEFAULT 9999,
  "patient_age_unit" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT '岁'::character varying,
  "patient_birthday" "pg_catalog"."timestamp",
  "patient_type" "pg_catalog"."int2",
  "check_item_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_item_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_org_item_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_org_item_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_modality_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_date_time" "pg_catalog"."timestamp",
  "check_site_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_site_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "apply_form_no_array" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "hospital_report_status" "pg_catalog"."int2",
  "report_no" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "report_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "report_result_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "report_org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "report_org_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "reports_advise" "pg_catalog"."text" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "report_date_time" "pg_catalog"."timestamp",
  "report_dept_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "report_dept_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "report_doctor_id_card" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "report_doctor_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "report_doctor_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "verify_org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "verify_org_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "verify_doctor_id_card" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "verify_doctor_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "verify_doctor_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "verify_date_time" "pg_catalog"."timestamp",
  "see_desc" "pg_catalog"."text" COLLATE "pg_catalog"."default",
  "result_desc" "pg_catalog"."text" COLLATE "pg_catalog"."default",
  "naked_eye_see_desc" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "lens_see_desc" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "diagnosis_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "diagnosis_name" "pg_catalog"."text" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "diagnosis_date" "pg_catalog"."timestamp",
  "diagnosis_org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "diagnosis_org_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "negative_positive" "pg_catalog"."int2" DEFAULT 0,
  "is_critical" "pg_catalog"."int2",
  "report_critical" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "critical_report_date_time" "pg_catalog"."timestamp",
  "critical_report_doctor_nanme" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "critical_report_doctor_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "critical_proce_value" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "critical_proce_status" "pg_catalog"."int2" DEFAULT 0,
  "critical_proce_date_time" "pg_catalog"."timestamp",
  "critical_proce_doctor_nanme" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "critical_proce_doctor_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "report_share_flag" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT '0'::character varying,
  "report_status" "pg_catalog"."int2" DEFAULT 2,
  "download_fail_reason" "pg_catalog"."text" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "download_begin_time" "pg_catalog"."timestamp",
  "download_end_time" "pg_catalog"."timestamp",
  "download_spend_time" "pg_catalog"."int8",
  "reexamine_date_time" "pg_catalog"."timestamp",
  "hospital_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "hospital_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "hospital_area" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "reexamine_doctor_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "reexamine_doctor_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "print_date_time" "pg_catalog"."timestamp",
  "print_count" "pg_catalog"."int8" DEFAULT 0,
  "pa_chk_param" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "pa_chk_flags" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "pa_chk_bd_flags" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "pa_chk_wy_flags" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "pa_delay_reason" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_share_flag" "pg_catalog"."int2",
  "is_merge" "pg_catalog"."int2",
  "check_spt_item_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "qc_status" "pg_catalog"."int4"
)
;
COMMENT ON COLUMN "public"."t_report"."id" IS '主键id';
COMMENT ON COLUMN "public"."t_report"."is_deleted" IS '是否删除: 1.删除 0.未删除';
COMMENT ON COLUMN "public"."t_report"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_report"."create_user" IS '创建者';
COMMENT ON COLUMN "public"."t_report"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_report"."update_user" IS '更新者';
COMMENT ON COLUMN "public"."t_report"."org_code" IS '机构编码';
COMMENT ON COLUMN "public"."t_report"."org_name" IS '机构名称';
COMMENT ON COLUMN "public"."t_report"."study_id" IS 'studyId';
COMMENT ON COLUMN "public"."t_report"."system_id" IS '信息系统在医院的唯一ID，该字段用来解决同一家医院不同系统中RIS_STUDY_ID相同的问题。建议从pacs01开始';
COMMENT ON COLUMN "public"."t_report"."annet_id" IS '在平台中唯一标识一份检查的ID';
COMMENT ON COLUMN "public"."t_report"."report_uid" IS '报告唯一编号';
COMMENT ON COLUMN "public"."t_report"."check_type" IS '检查系统类型：0放射类、1超声类、2内镜类、3核医学类、4病理类、5介入类、6心电类、7口腔类、8.眼科类 9.肺功能类 10.睡眠呼吸';
COMMENT ON COLUMN "public"."t_report"."ris_study_id" IS 'Ris登记流水号';
COMMENT ON COLUMN "public"."t_report"."is_disable" IS '是否禁用:0.否 1.是';
COMMENT ON COLUMN "public"."t_report"."patient_id" IS '患者ID';
COMMENT ON COLUMN "public"."t_report"."patient_no" IS '住院号/门诊号，与就诊类型相对应';
COMMENT ON COLUMN "public"."t_report"."patient_serial_no" IS '就诊流水号，与就诊类型相对应';
COMMENT ON COLUMN "public"."t_report"."patient_case_history_id" IS '患者病历号';
COMMENT ON COLUMN "public"."t_report"."patient_id_card_type" IS '患者身份证号，0.居民身份证 1.中国人民解放军军人身份证件 2.中国人民武装警察身份证件 3.港澳居民来往内地通行证 4.台湾居民来往大陆通行证 5.护照 6.其他';
COMMENT ON COLUMN "public"."t_report"."patient_id_card" IS '患者身份证号';
COMMENT ON COLUMN "public"."t_report"."patient_name" IS '患者姓名，个体在公安管理部门正式登记注册的姓氏和名称，此处指患者的姓名';
COMMENT ON COLUMN "public"."t_report"."patient_pinyin" IS '患者姓名，拼音';
COMMENT ON COLUMN "public"."t_report"."patient_gender" IS '患者性别：0.未知 1.男 2.女';
COMMENT ON COLUMN "public"."t_report"."patient_age" IS '患者年龄，9999无年龄';
COMMENT ON COLUMN "public"."t_report"."patient_age_unit" IS '患者年龄单位：岁/月/日/小时/无年龄';
COMMENT ON COLUMN "public"."t_report"."patient_birthday" IS '患者出生日期，精确到天（录入信息，非身份证提取）';
COMMENT ON COLUMN "public"."t_report"."patient_type" IS '患者类型：0.体检 1.门诊 2.急诊 3.住院 4.新生儿 5.转诊 6.其他';
COMMENT ON COLUMN "public"."t_report"."check_item_code" IS '检查项目标准编码';
COMMENT ON COLUMN "public"."t_report"."check_item_name" IS '检查项目描述';
COMMENT ON COLUMN "public"."t_report"."check_org_item_code" IS '检查项目医院编码';
COMMENT ON COLUMN "public"."t_report"."check_org_item_name" IS '检查项目医院名称';
COMMENT ON COLUMN "public"."t_report"."check_modality_type" IS '检查设备类型';
COMMENT ON COLUMN "public"."t_report"."check_date_time" IS '检查日期时间';
COMMENT ON COLUMN "public"."t_report"."check_site_code" IS '检查部位编码，多值用英文逗号隔开合并';
COMMENT ON COLUMN "public"."t_report"."check_site_name" IS '检查部位名称，多值用英文逗号隔开合并';
COMMENT ON COLUMN "public"."t_report"."apply_form_no_array" IS '检查申请单编号，多个申请单号以英文逗号隔开合并';
COMMENT ON COLUMN "public"."t_report"."hospital_report_status" IS '报告状态：0.开立 1.核对（护士核对）2.采样（病理入库，其他系统为已登记）3.执行（住院计费，已执行）4.已报告 5.审核 6.发布签发（发送报告消息，医护人员能立即查看报告。患者30分钟后方可查看）7.his取消申请单 8.其他 9.已打印';
COMMENT ON COLUMN "public"."t_report"."report_no" IS '医院检查报告编号，用于关联打印的报告单上的号，多值用英文逗号隔开合并';
COMMENT ON COLUMN "public"."t_report"."report_name" IS '医院检查报告名称';
COMMENT ON COLUMN "public"."t_report"."report_result_id" IS '检查结果唯一编号，用于关联t_report_file、 t_report_image，多值用英文逗号隔开合并';
COMMENT ON COLUMN "public"."t_report"."report_org_code" IS '报告医疗机构代码';
COMMENT ON COLUMN "public"."t_report"."report_org_name" IS '报告医疗机构名称';
COMMENT ON COLUMN "public"."t_report"."reports_advise" IS '报告建议';
COMMENT ON COLUMN "public"."t_report"."report_date_time" IS '报告日期时间';
COMMENT ON COLUMN "public"."t_report"."report_dept_code" IS '报告科室编码';
COMMENT ON COLUMN "public"."t_report"."report_dept_name" IS '报告科室名称';
COMMENT ON COLUMN "public"."t_report"."report_doctor_id_card" IS '报告医师身份证号';
COMMENT ON COLUMN "public"."t_report"."report_doctor_code" IS '报告医师编号';
COMMENT ON COLUMN "public"."t_report"."report_doctor_name" IS '报告医师姓名';
COMMENT ON COLUMN "public"."t_report"."verify_org_code" IS '审核医疗机构编号';
COMMENT ON COLUMN "public"."t_report"."verify_org_name" IS '审核医疗机构名称';
COMMENT ON COLUMN "public"."t_report"."verify_doctor_id_card" IS '审核医师身份证号';
COMMENT ON COLUMN "public"."t_report"."verify_doctor_code" IS '审核医生编码';
COMMENT ON COLUMN "public"."t_report"."verify_doctor_name" IS '审核医生名称';
COMMENT ON COLUMN "public"."t_report"."verify_date_time" IS '审核日期时间';
COMMENT ON COLUMN "public"."t_report"."see_desc" IS '所见';
COMMENT ON COLUMN "public"."t_report"."result_desc" IS '结论';
COMMENT ON COLUMN "public"."t_report"."naked_eye_see_desc" IS '肉眼所见';
COMMENT ON COLUMN "public"."t_report"."lens_see_desc" IS '镜下所见';
COMMENT ON COLUMN "public"."t_report"."diagnosis_code" IS '诊断代码';
COMMENT ON COLUMN "public"."t_report"."diagnosis_name" IS '诊断名称';
COMMENT ON COLUMN "public"."t_report"."diagnosis_date" IS '诊断日期时间';
COMMENT ON COLUMN "public"."t_report"."diagnosis_org_code" IS '诊断机构编码';
COMMENT ON COLUMN "public"."t_report"."diagnosis_org_name" IS '诊断机构名称';
COMMENT ON COLUMN "public"."t_report"."negative_positive" IS '阴阳性: 0.未知 1.阴性 2.阳性';
COMMENT ON COLUMN "public"."t_report"."is_critical" IS '危急值标识 0.否 1.是';
COMMENT ON COLUMN "public"."t_report"."report_critical" IS '危急值描述';
COMMENT ON COLUMN "public"."t_report"."critical_report_date_time" IS '危急值上报时间';
COMMENT ON COLUMN "public"."t_report"."critical_report_doctor_nanme" IS '危急值上报医生姓名';
COMMENT ON COLUMN "public"."t_report"."critical_report_doctor_code" IS '危急值上报医生his工号';
COMMENT ON COLUMN "public"."t_report"."critical_proce_value" IS '危急值处理结果';
COMMENT ON COLUMN "public"."t_report"."critical_proce_status" IS '危急值状态：0.无危急值 1.未发送 2.已发送 3.已处理';
COMMENT ON COLUMN "public"."t_report"."critical_proce_date_time" IS '危急值处理时间';
COMMENT ON COLUMN "public"."t_report"."critical_proce_doctor_nanme" IS '危急值处理医生姓名';
COMMENT ON COLUMN "public"."t_report"."critical_proce_doctor_code" IS '危急值上报医生his工号';
COMMENT ON COLUMN "public"."t_report"."report_share_flag" IS '是否与其他机构影像互认：0.非互认项目 1.互认项目，多个用逗号分隔';
COMMENT ON COLUMN "public"."t_report"."report_status" IS '报告采集状态：0.失败 1.成功 2.待下载 3.下载中';
COMMENT ON COLUMN "public"."t_report"."download_fail_reason" IS '失败原因';
COMMENT ON COLUMN "public"."t_report"."download_begin_time" IS '下载开始时间';
COMMENT ON COLUMN "public"."t_report"."download_end_time" IS '下载结束时间';
COMMENT ON COLUMN "public"."t_report"."download_spend_time" IS '下载耗时（单位：秒）';
COMMENT ON COLUMN "public"."t_report"."reexamine_date_time" IS '复审时间';
COMMENT ON COLUMN "public"."t_report"."hospital_code" IS '患者所在医疗机构编码';
COMMENT ON COLUMN "public"."t_report"."hospital_name" IS '患者所在医疗机构名称';
COMMENT ON COLUMN "public"."t_report"."hospital_area" IS '院区名称';
COMMENT ON COLUMN "public"."t_report"."reexamine_doctor_code" IS '复审医生编码';
COMMENT ON COLUMN "public"."t_report"."reexamine_doctor_name" IS '复审医生名称';
COMMENT ON COLUMN "public"."t_report"."print_date_time" IS '报告打印时间';
COMMENT ON COLUMN "public"."t_report"."print_count" IS '报告打印次数';
COMMENT ON COLUMN "public"."t_report"."pa_chk_param" IS '肉眼所见';
COMMENT ON COLUMN "public"."t_report"."pa_chk_flags" IS '常规符合';
COMMENT ON COLUMN "public"."t_report"."pa_chk_bd_flags" IS '冰冻符合';
COMMENT ON COLUMN "public"."t_report"."pa_chk_wy_flags" IS '外院符合';
COMMENT ON COLUMN "public"."t_report"."pa_delay_reason" IS '延发报告原因';
COMMENT ON TABLE "public"."t_report" IS '报告信息';

-- ----------------------------
-- Table structure for t_report_file
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_report_file";
CREATE TABLE "public"."t_report_file" (
  "id" "pg_catalog"."numeric" NOT NULL,
  "is_deleted" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "create_time" "pg_catalog"."timestamp" NOT NULL,
  "create_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "update_time" "pg_catalog"."timestamp" NOT NULL,
  "update_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "study_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "system_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "annet_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "report_no" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "report_result_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "node_type" "pg_catalog"."int4",
  "node_host" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "node_user_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "node_password" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "node_port" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "bucket_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "file_path" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "file_size" "pg_catalog"."numeric" NOT NULL DEFAULT 0,
  "file_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "sort_no" "pg_catalog"."int4" NOT NULL DEFAULT 0,
  "node_id" "pg_catalog"."numeric"
)
;
COMMENT ON COLUMN "public"."t_report_file"."id" IS 'id';
COMMENT ON COLUMN "public"."t_report_file"."is_deleted" IS '是否删除: 1.删除 0.未删除';
COMMENT ON COLUMN "public"."t_report_file"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_report_file"."create_user" IS '创建者';
COMMENT ON COLUMN "public"."t_report_file"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_report_file"."update_user" IS '更新者';
COMMENT ON COLUMN "public"."t_report_file"."org_code" IS '机构编码';
COMMENT ON COLUMN "public"."t_report_file"."study_id" IS 'studyId';
COMMENT ON COLUMN "public"."t_report_file"."system_id" IS '信息系统在医院的唯一ID，该字段用来解决同一家医院不同系统中RIS_STUDY_ID相同的问题。建议从pacs01开始';
COMMENT ON COLUMN "public"."t_report_file"."annet_id" IS '在平台中唯一标识一份检查的ID';
COMMENT ON COLUMN "public"."t_report_file"."report_no" IS '报告单编号,报告唯一标识';
COMMENT ON COLUMN "public"."t_report_file"."report_result_id" IS '检查结果唯一编号';
COMMENT ON COLUMN "public"."t_report_file"."node_type" IS '节点类型:0.本地 1.FTP 2.LAN 3.S3 4.Feign 5.HTTP';
COMMENT ON COLUMN "public"."t_report_file"."node_host" IS '节点地址';
COMMENT ON COLUMN "public"."t_report_file"."node_user_name" IS '连接用户名';
COMMENT ON COLUMN "public"."t_report_file"."node_password" IS '连接密码';
COMMENT ON COLUMN "public"."t_report_file"."node_port" IS '连接端口';
COMMENT ON COLUMN "public"."t_report_file"."bucket_name" IS '桶名称';
COMMENT ON COLUMN "public"."t_report_file"."file_path" IS '文件路径';
COMMENT ON COLUMN "public"."t_report_file"."file_size" IS '下载成功的文件大小（单位：k）';
COMMENT ON COLUMN "public"."t_report_file"."file_type" IS '文件类型';
COMMENT ON COLUMN "public"."t_report_file"."sort_no" IS '顺序号';
COMMENT ON COLUMN "public"."t_report_file"."node_id" IS '存储节点ID';
COMMENT ON TABLE "public"."t_report_file" IS '报告快照';

-- ----------------------------
-- Table structure for t_series
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_series";
CREATE TABLE "public"."t_series" (
  "id" "pg_catalog"."numeric" NOT NULL,
  "is_deleted" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "create_time" "pg_catalog"."timestamp" NOT NULL,
  "create_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "update_time" "pg_catalog"."timestamp" NOT NULL,
  "update_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "ic_id" "pg_catalog"."numeric" NOT NULL,
  "node_id" "pg_catalog"."numeric" NOT NULL,
  "study_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "annet_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "check_date_time" "pg_catalog"."timestamp" NOT NULL,
  "hospital_area" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "check_type" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "check_modality_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "download_begin_time" "pg_catalog"."timestamp",
  "download_end_time" "pg_catalog"."timestamp",
  "accession_number" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "study_instance_uid" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "series_instance_uid" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "series_number" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT '1'::character varying,
  "series_frames_number" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT 1,
  "series_description" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "task_type" "pg_catalog"."int2" NOT NULL DEFAULT 8,
  "bucket_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "node_host" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "node_user_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "node_password" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "node_port" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "node_type" "pg_catalog"."int2",
  "file_path" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "file_count" "pg_catalog"."int8" NOT NULL,
  "file_size" "pg_catalog"."numeric" NOT NULL DEFAULT 0,
  "file_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."t_series"."id" IS '主键id';
COMMENT ON COLUMN "public"."t_series"."is_deleted" IS '是否删除: 1.删除 0.未删除';
COMMENT ON COLUMN "public"."t_series"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_series"."create_user" IS '创建者';
COMMENT ON COLUMN "public"."t_series"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_series"."update_user" IS '更新者';
COMMENT ON COLUMN "public"."t_series"."org_code" IS '患者所在医疗机构编码';
COMMENT ON COLUMN "public"."t_series"."org_name" IS '患者所在医疗机构名称';
COMMENT ON COLUMN "public"."t_series"."ic_id" IS '影像中心id';
COMMENT ON COLUMN "public"."t_series"."node_id" IS '节点id';
COMMENT ON COLUMN "public"."t_series"."study_id" IS '检查号';
COMMENT ON COLUMN "public"."t_series"."annet_id" IS '在平台中唯一标识一份检查的ID';
COMMENT ON COLUMN "public"."t_series"."check_date_time" IS '检查时间';
COMMENT ON COLUMN "public"."t_series"."hospital_area" IS '院区名称';
COMMENT ON COLUMN "public"."t_series"."check_type" IS '检查系统类型：0放射类、1超声类、2内镜类、3核医学类、4病理类、5介入类、6心电类、7口腔类、8.眼科类 9.肺功能类 10.睡眠呼吸';
COMMENT ON COLUMN "public"."t_series"."check_modality_type" IS '设备类型';
COMMENT ON COLUMN "public"."t_series"."download_begin_time" IS '下载开始时间';
COMMENT ON COLUMN "public"."t_series"."download_end_time" IS '下载结束时间';
COMMENT ON COLUMN "public"."t_series"."accession_number" IS 'accessionNumber';
COMMENT ON COLUMN "public"."t_series"."study_instance_uid" IS 'StudyInstanceUid';
COMMENT ON COLUMN "public"."t_series"."series_instance_uid" IS 'SeriesInstanceUid';
COMMENT ON COLUMN "public"."t_series"."series_number" IS '序列号';
COMMENT ON COLUMN "public"."t_series"."series_frames_number" IS '序列帧数';
COMMENT ON COLUMN "public"."t_series"."series_description" IS '序列描述';
COMMENT ON COLUMN "public"."t_series"."task_type" IS '任务类型：0.FTP，1.QR，2.LAN 3.S3 4.SFTP 5.LOCAL 6.HTTP/HTTPS 7.QR_PASSIVE 8.未知';
COMMENT ON COLUMN "public"."t_series"."bucket_name" IS '桶名称';
COMMENT ON COLUMN "public"."t_series"."node_host" IS '节点地址';
COMMENT ON COLUMN "public"."t_series"."node_user_name" IS '节点用户名';
COMMENT ON COLUMN "public"."t_series"."node_password" IS '节点密码';
COMMENT ON COLUMN "public"."t_series"."node_port" IS '节点端口';
COMMENT ON COLUMN "public"."t_series"."node_type" IS '节点类型:0.本地 1.FTP 2.LAN 3.S3 4.Feign 5.HTTP';
COMMENT ON COLUMN "public"."t_series"."file_path" IS '文件路径';
COMMENT ON COLUMN "public"."t_series"."file_count" IS '文件数';
COMMENT ON COLUMN "public"."t_series"."file_size" IS '下载成功的文件大小（单位：k）';
COMMENT ON COLUMN "public"."t_series"."file_type" IS '文件类型';
COMMENT ON TABLE "public"."t_series" IS '序列信息';

-- ----------------------------
-- Table structure for t_study
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_study";
CREATE TABLE "public"."t_study" (
  "id" "pg_catalog"."numeric" NOT NULL,
  "is_deleted" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "create_time" "pg_catalog"."timestamp" NOT NULL,
  "create_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "update_time" "pg_catalog"."timestamp" NOT NULL,
  "update_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "system_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "study_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "study_no" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "annet_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "ris_study_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "radiation_no" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "is_disable" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "apply_form_no_array" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "apply_date_time" "pg_catalog"."timestamp",
  "apply_org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "apply_org_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "apply_dept_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "apply_dept_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "apply_doctor_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "diagnosis" "pg_catalog"."text" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "ward_bed_no" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_item_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_item_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_org_item_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_org_item_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_price_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_site_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_site_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_dept_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_dept_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_share_flag" "pg_catalog"."int2",
  "check_status" "pg_catalog"."int2",
  "check_type" "pg_catalog"."int2",
  "check_dev_room" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_dev_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_method" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_scan_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_item_price" "pg_catalog"."numeric",
  "insurance_price" "pg_catalog"."numeric",
  "patient_pay_price" "pg_catalog"."numeric",
  "check_modality_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "register_date_time" "pg_catalog"."timestamp",
  "register_doctor_id_card" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "register_doctor_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "arrive_date_time" "pg_catalog"."timestamp",
  "check_date_time" "pg_catalog"."timestamp",
  "check_doctor_id_card" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_doctor_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_doctor_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_no" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_serial_no" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_case_history_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_id_card_type" "pg_catalog"."int2" DEFAULT 6,
  "patient_id_card" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_id_card_verify" "pg_catalog"."int2" DEFAULT 0,
  "patient_id_card_hos" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "patient_pinyin" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_gender" "pg_catalog"."int2",
  "patient_age" "pg_catalog"."int8" DEFAULT 9999,
  "patient_age_unit" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT '岁'::character varying,
  "patient_birthday" "pg_catalog"."timestamp",
  "patient_phone" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "patient_type" "pg_catalog"."int2",
  "image_status" "pg_catalog"."int2" DEFAULT 2,
  "download_fail_reason" "pg_catalog"."text" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "download_begin_time" "pg_catalog"."timestamp",
  "download_end_time" "pg_catalog"."timestamp",
  "download_spend_time" "pg_catalog"."int8",
  "file_size" "pg_catalog"."numeric" DEFAULT 0,
  "qr_status" "pg_catalog"."int2" DEFAULT 0,
  "qr_download_begin_time" "pg_catalog"."timestamp",
  "qr_download_end_time" "pg_catalog"."timestamp",
  "is_jpg" "pg_catalog"."int2" DEFAULT 0,
  "pa_income" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "pa_outter_gm_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "pa_sample_count" "pg_catalog"."int8",
  "pa_candle_count" "pg_catalog"."int8",
  "pa_glass_count" "pg_catalog"."int8",
  "pa_ihc_count" "pg_catalog"."int8",
  "pa_sample_image_count" "pg_catalog"."int8",
  "pa_freeze_flag" "pg_catalog"."int8",
  "pa_fast_candle_flag" "pg_catalog"."int8",
  "pa_sample_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "pa_block_count" "pg_catalog"."int8",
  "study_instance_uid" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;
COMMENT ON COLUMN "public"."t_study"."id" IS '主键id';
COMMENT ON COLUMN "public"."t_study"."is_deleted" IS '是否删除: 1.删除 0.未删除';
COMMENT ON COLUMN "public"."t_study"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_study"."create_user" IS '创建者';
COMMENT ON COLUMN "public"."t_study"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_study"."update_user" IS '更新者';
COMMENT ON COLUMN "public"."t_study"."org_code" IS '对接的医疗机构代码，采用18位“统一社会信用代码”';
COMMENT ON COLUMN "public"."t_study"."org_name" IS '医疗机构第一执业许可名称';
COMMENT ON COLUMN "public"."t_study"."system_id" IS '信息系统在医院的唯一ID，该字段用来解决同一家医院不同系统中RIS_STUDY_ID相同的问题。建议从pacs01开始';
COMMENT ON COLUMN "public"."t_study"."study_id" IS '检查流水号';
COMMENT ON COLUMN "public"."t_study"."study_no" IS '检查编号';
COMMENT ON COLUMN "public"."t_study"."annet_id" IS '在平台中检查唯一标识符';
COMMENT ON COLUMN "public"."t_study"."ris_study_id" IS 'Ris登记流水号';
COMMENT ON COLUMN "public"."t_study"."radiation_no" IS '放射号';
COMMENT ON COLUMN "public"."t_study"."is_disable" IS '是否禁用 0.是 1.否';
COMMENT ON COLUMN "public"."t_study"."apply_form_no_array" IS '检查申请单编号，多个申请单号以英文逗号隔开合并';
COMMENT ON COLUMN "public"."t_study"."apply_date_time" IS '申请日期时间，医生在系统中发起检查申请的日期时间';
COMMENT ON COLUMN "public"."t_study"."apply_org_code" IS '对接的医疗机构代码，采用18位“统一社会信用代码”';
COMMENT ON COLUMN "public"."t_study"."apply_org_name" IS '医疗机构第一执业许可名称';
COMMENT ON COLUMN "public"."t_study"."apply_dept_name" IS '申请科室名称';
COMMENT ON COLUMN "public"."t_study"."apply_dept_code" IS '申请科室编码';
COMMENT ON COLUMN "public"."t_study"."apply_doctor_name" IS '申请医生姓名，个体在公安管理部门正式登记注册的姓氏和名称，此处指申请医生的姓名';
COMMENT ON COLUMN "public"."t_study"."diagnosis" IS '初步诊断';
COMMENT ON COLUMN "public"."t_study"."ward_bed_no" IS '病房床号';
COMMENT ON COLUMN "public"."t_study"."check_item_code" IS '检查项目标准编码';
COMMENT ON COLUMN "public"."t_study"."check_item_name" IS '检查项目描述';
COMMENT ON COLUMN "public"."t_study"."check_org_item_code" IS '检查项目医院编码';
COMMENT ON COLUMN "public"."t_study"."check_org_item_name" IS '检查项目医院名称';
COMMENT ON COLUMN "public"."t_study"."check_price_code" IS '物价检查项目编码，多值用英文逗号隔开合并';
COMMENT ON COLUMN "public"."t_study"."check_site_code" IS '检查部位编码，多值用英文逗号隔开合并';
COMMENT ON COLUMN "public"."t_study"."check_site_name" IS '检查部位名称，多值用英文逗号隔开合并';
COMMENT ON COLUMN "public"."t_study"."check_dept_code" IS '检查科室编码';
COMMENT ON COLUMN "public"."t_study"."check_dept_name" IS '检查科室名称';
COMMENT ON COLUMN "public"."t_study"."check_share_flag" IS '是否与其他机构影像互认：0.非互认项目 1.互认项目';
COMMENT ON COLUMN "public"."t_study"."check_status" IS '检查状态：0.开立 1.核对（护士核对）2.采样（病理入库，其他系统为已登记）3.执行（住院计费，已执行）4.已报告 5.审核 6.发布签发（发送报告消息，医护人员能立即查看报告。患者30分钟后方可查看）7.his取消申请单 8.其他';
COMMENT ON COLUMN "public"."t_study"."check_type" IS '检查系统类型：0放射类、1超声类、2内镜类、3核医学类、4病理类、5介入类、6心电类、7口腔类、8.眼科类 9.肺功能类 10.睡眠呼吸';
COMMENT ON COLUMN "public"."t_study"."check_dev_room" IS '设备房间，多值用英文逗号隔开合并';
COMMENT ON COLUMN "public"."t_study"."check_dev_name" IS '设备名称，多值用英文逗号隔开合并';
COMMENT ON COLUMN "public"."t_study"."check_method" IS '检查方法';
COMMENT ON COLUMN "public"."t_study"."check_scan_type" IS '扫描方式';
COMMENT ON COLUMN "public"."t_study"."check_item_price" IS '检查项目价格(单位：元)';
COMMENT ON COLUMN "public"."t_study"."insurance_price" IS '医保支付金额(单位：元)';
COMMENT ON COLUMN "public"."t_study"."patient_pay_price" IS '患者支付金额(单位：元)';
COMMENT ON COLUMN "public"."t_study"."check_modality_type" IS '检查设备类型';
COMMENT ON COLUMN "public"."t_study"."register_date_time" IS '登记日期时间';
COMMENT ON COLUMN "public"."t_study"."register_doctor_id_card" IS '登记人身份证号';
COMMENT ON COLUMN "public"."t_study"."register_doctor_name" IS '登记人姓名';
COMMENT ON COLUMN "public"."t_study"."arrive_date_time" IS '患者到检日期时间';
COMMENT ON COLUMN "public"."t_study"."check_date_time" IS '检查日期时间';
COMMENT ON COLUMN "public"."t_study"."check_doctor_id_card" IS '检查技师身份证号';
COMMENT ON COLUMN "public"."t_study"."check_doctor_code" IS '检查技师编号';
COMMENT ON COLUMN "public"."t_study"."check_doctor_name" IS '检查技师姓名';
COMMENT ON COLUMN "public"."t_study"."patient_id" IS '患者ID';
COMMENT ON COLUMN "public"."t_study"."patient_no" IS '住院号/门诊号，与就诊类型相对应';
COMMENT ON COLUMN "public"."t_study"."patient_serial_no" IS '就诊流水号，与就诊类型相对应';
COMMENT ON COLUMN "public"."t_study"."patient_case_history_id" IS '患者病历号';
COMMENT ON COLUMN "public"."t_study"."patient_id_card_type" IS '患者身份证号，0.居民身份证 1.中国人民解放军军人身份证件 2.中国人民武装警察身份证件 3.港澳居民来往内地通行证 4.台湾居民来往大陆通行证 5.护照 6.其他';
COMMENT ON COLUMN "public"."t_study"."patient_id_card" IS '患者身份证号。';
COMMENT ON COLUMN "public"."t_study"."patient_id_card_verify" IS '患者身份证合法性:0.合法 1.非法';
COMMENT ON COLUMN "public"."t_study"."patient_id_card_hos" IS '医保卡号，患者的医保卡上的唯一法定标识符';
COMMENT ON COLUMN "public"."t_study"."patient_name" IS '患者姓名，个体在公安管理部门正式登记注册的姓氏和名称，此处指患者的姓名';
COMMENT ON COLUMN "public"."t_study"."patient_pinyin" IS '患者姓名，拼音';
COMMENT ON COLUMN "public"."t_study"."patient_gender" IS '患者性别： 0.未知 1.男 2.女';
COMMENT ON COLUMN "public"."t_study"."patient_age" IS '患者年龄，9999无年龄';
COMMENT ON COLUMN "public"."t_study"."patient_age_unit" IS '患者年龄单位：岁/月/日/小时/无年龄';
COMMENT ON COLUMN "public"."t_study"."patient_birthday" IS '患者出生日期，精确到天（录入信息，非身份证提取）';
COMMENT ON COLUMN "public"."t_study"."patient_phone" IS '患者手机号码';
COMMENT ON COLUMN "public"."t_study"."patient_type" IS '患者类型：0.体检 1.门诊 2.急诊 3.住院 4.新生儿 5.转诊 6.其他';
COMMENT ON COLUMN "public"."t_study"."image_status" IS '图像采集状态：0.失败 1.成功 2.待下载 3.下载中 4.已删除';
COMMENT ON COLUMN "public"."t_study"."download_fail_reason" IS '失败原因';
COMMENT ON COLUMN "public"."t_study"."download_begin_time" IS '下载开始时间';
COMMENT ON COLUMN "public"."t_study"."download_end_time" IS '下载结束时间';
COMMENT ON COLUMN "public"."t_study"."download_spend_time" IS '下载耗时（单位：秒）';
COMMENT ON COLUMN "public"."t_study"."file_size" IS '下载成功的文件大小（单位：k）';
COMMENT ON COLUMN "public"."t_study"."qr_status" IS '是否支持qr检索: 0.待下载 1.下载中 2.成功 3.失败 4.禁用';
COMMENT ON COLUMN "public"."t_study"."qr_download_begin_time" IS 'qr采集开始时间';
COMMENT ON COLUMN "public"."t_study"."qr_download_end_time" IS 'qr采集结束时间';
COMMENT ON COLUMN "public"."t_study"."is_jpg" IS '是否生成jpg: 0.否 1.是';
COMMENT ON COLUMN "public"."t_study"."pa_income" IS '该次检查设备收入';
COMMENT ON COLUMN "public"."t_study"."pa_outter_gm_id" IS '原病理号';
COMMENT ON COLUMN "public"."t_study"."pa_sample_count" IS '标本数';
COMMENT ON COLUMN "public"."t_study"."pa_candle_count" IS '蜡块数';
COMMENT ON COLUMN "public"."t_study"."pa_glass_count" IS '玻片数';
COMMENT ON COLUMN "public"."t_study"."pa_ihc_count" IS '免疫组化数';
COMMENT ON COLUMN "public"."t_study"."pa_sample_image_count" IS '标本图像数';
COMMENT ON COLUMN "public"."t_study"."pa_freeze_flag" IS '是否冰冻：0.否 1.是';
COMMENT ON COLUMN "public"."t_study"."pa_fast_candle_flag" IS '是否快蜡：0.否 1.是';
COMMENT ON COLUMN "public"."t_study"."pa_sample_type" IS '标本类型';
COMMENT ON COLUMN "public"."t_study"."pa_block_count" IS '材块数量';
COMMENT ON TABLE "public"."t_study" IS '检查信息';

-- ----------------------------
-- Table structure for t_task
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_task";
CREATE TABLE "public"."t_task" (
  "task_id" "pg_catalog"."numeric" NOT NULL,
  "is_deleted" "pg_catalog"."int2" NOT NULL DEFAULT 0,
  "create_time" "pg_catalog"."timestamp" NOT NULL,
  "create_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "update_time" "pg_catalog"."timestamp" NOT NULL,
  "update_user" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "org_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "study_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "system_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "annet_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "report_uid" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "check_date_time" "pg_catalog"."timestamp",
  "report_date_time" "pg_catalog"."timestamp",
  "dicom_param" "pg_catalog"."text" COLLATE "pg_catalog"."default",
  "dicom_param_type" "pg_catalog"."int2",
  "dicom_param_bucket_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "check_type" "pg_catalog"."int2" DEFAULT 0,
  "check_modality_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "apply_dept_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "apply_dept_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "apply_dept_sort" "pg_catalog"."int8" DEFAULT 0,
  "task_type" "pg_catalog"."int2" DEFAULT 1,
  "task_field" "pg_catalog"."int2" DEFAULT 0,
  "task_status" "pg_catalog"."int2" DEFAULT 0,
  "receive_time" "pg_catalog"."timestamp",
  "receiver_ic_id" "pg_catalog"."numeric",
  "receiver_ic_ip" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "receiver_node_id" "pg_catalog"."numeric",
  "receiver_node_type" "pg_catalog"."int2",
  "receiver_node_file_path" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "patient_type" "pg_catalog"."int2",
  "admin_sort" "pg_catalog"."int8" DEFAULT 0,
  "user_sort" "pg_catalog"."int8" DEFAULT 0,
  "unlock_time" "pg_catalog"."timestamp" DEFAULT CURRENT_TIMESTAMP,
  "download_fail_count" "pg_catalog"."int8" DEFAULT 0
)
;
COMMENT ON COLUMN "public"."t_task"."task_id" IS '任务id';
COMMENT ON COLUMN "public"."t_task"."is_deleted" IS '是否删除: 1.删除 0.未删除';
COMMENT ON COLUMN "public"."t_task"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_task"."create_user" IS '创建者';
COMMENT ON COLUMN "public"."t_task"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_task"."update_user" IS '更新者';
COMMENT ON COLUMN "public"."t_task"."org_code" IS '机构编码';
COMMENT ON COLUMN "public"."t_task"."org_name" IS '机构名称';
COMMENT ON COLUMN "public"."t_task"."study_id" IS '检查号';
COMMENT ON COLUMN "public"."t_task"."system_id" IS '信息系统在医院的唯一ID，该字段用来解决同一家医院不同系统中RIS_STUDY_ID相同的问题。建议从pacs01开始';
COMMENT ON COLUMN "public"."t_task"."annet_id" IS '在平台中检查唯一标识符';
COMMENT ON COLUMN "public"."t_task"."report_uid" IS '报告唯一编号';
COMMENT ON COLUMN "public"."t_task"."check_date_time" IS '检查时间';
COMMENT ON COLUMN "public"."t_task"."report_date_time" IS '报告时间';
COMMENT ON COLUMN "public"."t_task"."dicom_param" IS '取图号，json数组格式';
COMMENT ON COLUMN "public"."t_task"."dicom_param_type" IS '取图号类型 0.AccessionNumber 1.StudyInstanceUID 2.文件目录 3.文件路径 4.无图像 5.[任务类型]:[账号]:[密码]:[地址]:[路径] 6.[任务类型]:[本地AE]:[本地Port]:[服务端IP]:[服务端AE]:[服务端Port] 7.无报告快照 8.无图文报告';
COMMENT ON COLUMN "public"."t_task"."dicom_param_bucket_name" IS '取图桶名称';
COMMENT ON COLUMN "public"."t_task"."check_type" IS '检查系统类型：0放射类、1超声类、2内镜类、3核医学类、4病理类、5介入类、6心电类、7口腔类、8.眼科类 9.肺功能类 10.睡眠呼吸';
COMMENT ON COLUMN "public"."t_task"."check_modality_type" IS '检查设备类型';
COMMENT ON COLUMN "public"."t_task"."apply_dept_code" IS '申请科室编码';
COMMENT ON COLUMN "public"."t_task"."apply_dept_name" IS '申请科室名';
COMMENT ON COLUMN "public"."t_task"."apply_dept_sort" IS '申请科室序号：排序,大前小后。0为无科室';
COMMENT ON COLUMN "public"."t_task"."task_type" IS '任务类型：0.FTP，1.QR，2.LAN 3.S3 4.SFTP 5.LOCAL 6.HTTP/HTTPS 7.QR_PASSIVE';
COMMENT ON COLUMN "public"."t_task"."task_field" IS '任务属性：0.图像 1.报告快照 2.图文报告 3.申请单';
COMMENT ON COLUMN "public"."t_task"."task_status" IS '任务状态：0.未下载 1.下载中 2.成功';
COMMENT ON COLUMN "public"."t_task"."receive_time" IS '当前任务被接收时间';
COMMENT ON COLUMN "public"."t_task"."receiver_ic_id" IS '当前任务接收者影像中心id';
COMMENT ON COLUMN "public"."t_task"."receiver_ic_ip" IS '当前任务接收者影像中心ip';
COMMENT ON COLUMN "public"."t_task"."receiver_node_id" IS '当前任务接收者节点id';
COMMENT ON COLUMN "public"."t_task"."receiver_node_type" IS '当前任务接收者节点类型:0.本地 1.FTP 2.LAN 3.S3 4.Feign 5.HTTP';
COMMENT ON COLUMN "public"."t_task"."receiver_node_file_path" IS '当前任务接收者节点路径';
COMMENT ON COLUMN "public"."t_task"."patient_type" IS '患者类型：0.体检 1.门诊 2.急诊 3.住院 4.新生儿 5.转诊 6.其他';
COMMENT ON COLUMN "public"."t_task"."admin_sort" IS '管理员排序:大前小后';
COMMENT ON COLUMN "public"."t_task"."user_sort" IS '用户排序:大前小后';
COMMENT ON COLUMN "public"."t_task"."unlock_time" IS '解锁时间：任务失败了会锁住任务，无法获取';
COMMENT ON COLUMN "public"."t_task"."download_fail_count" IS '采集失败次数';
COMMENT ON TABLE "public"."t_task" IS '任务表';

-- ----------------------------
-- Indexes structure for table t_download_record
-- ----------------------------
CREATE INDEX "idx_t_download_record_annet_id" ON "public"."t_download_record" USING btree (
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_download_record_download_begin_time" ON "public"."t_download_record" USING btree (
  "download_begin_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_download_record_download_end_time" ON "public"."t_download_record" USING btree (
  "download_end_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_download_record_download_spend_time" ON "public"."t_download_record" USING btree (
  "download_spend_time" "pg_catalog"."numeric_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_download_record_ic_id" ON "public"."t_download_record" USING btree (
  "ic_id" "pg_catalog"."numeric_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_download_record_node_id" ON "public"."t_download_record" USING btree (
  "node_id" "pg_catalog"."numeric_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_download_record_study_id" ON "public"."t_download_record" USING btree (
  "study_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_download_record
-- ----------------------------
ALTER TABLE "public"."t_download_record" ADD CONSTRAINT "t_download_record_pkey" PRIMARY KEY ("record_id");

-- ----------------------------
-- Primary Key structure for table t_ic_config
-- ----------------------------
ALTER TABLE "public"."t_ic_config" ADD CONSTRAINT "t_ic_config_pkey" PRIMARY KEY ("ic_id");

-- ----------------------------
-- Indexes structure for table t_process
-- ----------------------------
CREATE INDEX "idx_t_process_annet_id" ON "public"."t_process" USING btree (
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_process_check_date_time" ON "public"."t_process" USING btree (
  "check_date_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_process_code" ON "public"."t_process" USING btree (
  "code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_process_fail_count" ON "public"."t_process" USING btree (
  "fail_count" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_process_process_type" ON "public"."t_process" USING btree (
  "process_type" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_process_study_id" ON "public"."t_process" USING btree (
  "study_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_process
-- ----------------------------
ALTER TABLE "public"."t_process" ADD CONSTRAINT "t_process_pkey" PRIMARY KEY ("process_id");

-- ----------------------------
-- Indexes structure for table t_report
-- ----------------------------
CREATE INDEX "idx_t_org_report_no" ON "public"."t_report" USING btree (
  "org_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "report_no" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report" ON "public"."t_report" USING btree (
  "is_deleted" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "report_uid" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_annet_id" ON "public"."t_report" USING btree (
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_annet_id_report_status" ON "public"."t_report" USING btree (
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "report_status" "pg_catalog"."int2_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_create_time" ON "public"."t_report" USING btree (
  "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_date_time_annet_id_org_code" ON "public"."t_report" USING btree (
  "report_date_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST,
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "org_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_download_bt" ON "public"."t_report" USING btree (
  "download_begin_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_download_end_time" ON "public"."t_report" USING btree (
  "download_end_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_hospital_code" ON "public"."t_report" USING btree (
  "hospital_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_patient_id_card" ON "public"."t_report" USING btree (
  "patient_id_card" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_patient_report_date_time" ON "public"."t_report" USING btree (
  "report_date_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_patient_verify_date_time" ON "public"."t_report" USING btree (
  "verify_date_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_report_no" ON "public"."t_report" USING btree (
  "report_no" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_report_rid" ON "public"."t_report" USING btree (
  "report_result_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_report_status" ON "public"."t_report" USING btree (
  "report_status" "pg_catalog"."int2_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_study_id" ON "public"."t_report" USING btree (
  "study_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_update_time" ON "public"."t_report" USING btree (
  "update_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_report
-- ----------------------------
ALTER TABLE "public"."t_report" ADD CONSTRAINT "t_report_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_report_file
-- ----------------------------
CREATE INDEX "idx_t_report_file_annet_id" ON "public"."t_report_file" USING btree (
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_file_report_no" ON "public"."t_report_file" USING btree (
  "report_no" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_file_report_rid" ON "public"."t_report_file" USING btree (
  "report_result_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_report_file_study_id" ON "public"."t_report_file" USING btree (
  "study_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_report_file
-- ----------------------------
ALTER TABLE "public"."t_report_file" ADD CONSTRAINT "t_report_file_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_series
-- ----------------------------
CREATE INDEX "idx_t_series_accession_number" ON "public"."t_series" USING btree (
  "accession_number" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_series_annet_id" ON "public"."t_series" USING btree (
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_series_check_date_time" ON "public"."t_series" USING btree (
  "check_date_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_series_create_time" ON "public"."t_series" USING btree (
  "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_series_download_begin_time" ON "public"."t_series" USING btree (
  "download_begin_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_series_download_end_time" ON "public"."t_series" USING btree (
  "download_end_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_series_series_instance_uid" ON "public"."t_series" USING btree (
  "series_instance_uid" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_series_study_id" ON "public"."t_series" USING btree (
  "study_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_series_study_instance_uid" ON "public"."t_series" USING btree (
  "study_instance_uid" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_series_update_time" ON "public"."t_series" USING btree (
  "update_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_series
-- ----------------------------
ALTER TABLE "public"."t_series" ADD CONSTRAINT "t_series_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_study
-- ----------------------------
CREATE INDEX "idx_t_study_annet_id" ON "public"."t_study" USING btree (
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_study_check_date_time" ON "public"."t_study" USING btree (
  "check_date_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_study_create_time" ON "public"."t_study" USING btree (
  "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_study_download_begin_time" ON "public"."t_study" USING btree (
  "download_begin_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_study_download_end_time" ON "public"."t_study" USING btree (
  "download_end_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_study_is_deleted_annet_id" ON "public"."t_study" USING btree (
  "is_deleted" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_study_org_code_study_id" ON "public"."t_study" USING btree (
  "org_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "study_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_study_patient_id_card" ON "public"."t_study" USING btree (
  "patient_id_card" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_study_qr_download_begin_time" ON "public"."t_study" USING btree (
  "qr_download_begin_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_study_study_id" ON "public"."t_study" USING btree (
  "study_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_study_update_time" ON "public"."t_study" USING btree (
  "update_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "qr_download_end_time" ON "public"."t_study" USING btree (
  "qr_download_end_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_study
-- ----------------------------
ALTER TABLE "public"."t_study" ADD CONSTRAINT "t_study_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_task
-- ----------------------------
CREATE INDEX "idx_t_task_01" ON "public"."t_task" USING btree (
  "task_status" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "org_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "check_type" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_field" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_type" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "download_fail_count" "pg_catalog"."int8_ops" ASC NULLS LAST,
  "unlock_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_check_date_time" ON "public"."t_task" USING btree (
  "check_date_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_is_deleted_annet_id" ON "public"."t_task" USING btree (
  "is_deleted" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_is_deleted_annet_id_task_status_task_field" ON "public"."t_task" USING btree (
  "is_deleted" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "task_status" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_field" "pg_catalog"."int2_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_is_deleted_study_id_annet_id_task_field" ON "public"."t_task" USING btree (
  "is_deleted" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "study_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "task_field" "pg_catalog"."int2_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_is_deleted_task_status_check_date_time_org_code" ON "public"."t_task" USING btree (
  "is_deleted" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_status" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "check_date_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST,
  "org_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_is_deleted_task_status_report_date_time_org_code" ON "public"."t_task" USING btree (
  "is_deleted" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_status" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "report_date_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST,
  "org_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_report_date_time" ON "public"."t_task" USING btree (
  "report_date_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_study_id" ON "public"."t_task" USING btree (
  "study_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_u1" ON "public"."t_task" USING btree (
  "receiver_ic_id" "pg_catalog"."numeric_ops" ASC NULLS LAST,
  "task_status" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_field" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_type" "pg_catalog"."int2_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_u2" ON "public"."t_task" USING btree (
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "task_field" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_type" "pg_catalog"."int2_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_un1" ON "public"."t_task" USING btree (
  "is_deleted" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_status" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "org_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "check_type" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_field" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_type" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "download_fail_count" "pg_catalog"."int8_ops" ASC NULLS LAST,
  "unlock_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_un2" ON "public"."t_task" USING btree (
  "is_deleted" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_status" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "org_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "check_type" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "task_field" "pg_catalog"."int2_ops" ASC NULLS LAST,
  "download_fail_count" "pg_catalog"."int8_ops" ASC NULLS LAST,
  "unlock_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_t_task_unlock_time" ON "public"."t_task" USING btree (
  "unlock_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "t_task_annet_id" ON "public"."t_task" USING btree (
  "annet_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_task
-- ----------------------------
ALTER TABLE "public"."t_task" ADD CONSTRAINT "t_task_pkey" PRIMARY KEY ("task_id");
