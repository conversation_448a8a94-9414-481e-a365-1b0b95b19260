/*
================================================================================
镇沅彝族哈尼族拉祜族自治县人民医院 - 影像数据下载统计SQL维护文档
================================================================================

文档说明：
- 目标医院：镇沅彝族哈尼族拉祜族自治县人民医院
- 机构编码：12622827439150531N
- 机构名称：镇沅县人民医院
- 创建时间：2025-07-28
- 维护目的：提供影像数据下载统计分析和运维监控查询

数据库版本：PostgreSQL 16.0
表结构版本：基于ic.sql (2025-07-28)

================================================================================
数据字典说明
================================================================================

核心表结构：
1. t_task (任务表) - 主要统计数据源
2. t_download_record (下载记录表) - 下载详情和耗时信息
3. t_apply (申请信息表) - 检查申请相关信息

关键字段说明：
- task_field (任务属性)：0=图像, 1=报告快照, 2=图文报告, 3=申请单
- task_status (任务状态)：0=未下载, 1=下载中, 2=成功
- check_date_time：检查发生时间
- create_time：任务创建时间（下载任务生成时间）
- download_begin_time/download_end_time：实际下载时间段
- download_spend_time：下载耗时（单位：秒）

业务分类：
- 影像数据：task_field = 0
- PDF报告：task_field IN (1, 2) -- 报告快照和图文报告

================================================================================
一、按下载时间维度统计查询
================================================================================
*/

-- 1.1 每日影像数据下载统计
-- 功能：按下载任务创建日期统计影像数据的下载情况
-- 结果列：统计日期, 总数量, 成功数量, 未下载数量, 下载中数量, 成功率(%)
SELECT 
    DATE_TRUNC('day', create_time)::DATE AS 统计日期,
    COUNT(*) AS 总数量,
    COUNT(CASE WHEN task_status = 2 THEN 1 END) AS 成功数量,
    COUNT(CASE WHEN task_status = 0 THEN 1 END) AS 未下载数量,
    COUNT(CASE WHEN task_status = 1 THEN 1 END) AS 下载中数量,
    ROUND(
        COUNT(CASE WHEN task_status = 2 THEN 1 END) * 100.0 / COUNT(*), 2
    ) AS 成功率_百分比
FROM t_task 
WHERE is_deleted = 0 
    AND org_code = '12622827439150531N'
    AND task_field = 0  -- 图像数据
    AND create_time >= CURRENT_DATE - INTERVAL '30 days'  -- 最近30天
GROUP BY DATE_TRUNC('day', create_time)
ORDER BY 统计日期 DESC;

-- 示例结果：
-- 统计日期    | 总数量 | 成功数量 | 未下载数量 | 下载中数量 | 成功率_百分比
-- 2025-07-28 |   156  |   142   |     12    |     2     |    91.03
-- 2025-07-27 |   189  |   178   |      8    |     3     |    94.18

-- 1.2 每日PDF报告下载统计
-- 功能：按下载任务创建日期统计PDF报告（报告快照+图文报告）的下载情况
SELECT 
    DATE_TRUNC('day', create_time)::DATE AS 统计日期,
    COUNT(*) AS 总数量,
    COUNT(CASE WHEN task_status = 2 THEN 1 END) AS 成功数量,
    COUNT(CASE WHEN task_status = 0 THEN 1 END) AS 未下载数量,
    COUNT(CASE WHEN task_status = 1 THEN 1 END) AS 下载中数量,
    ROUND(
        COUNT(CASE WHEN task_status = 2 THEN 1 END) * 100.0 / COUNT(*), 2
    ) AS 成功率_百分比,
    COUNT(CASE WHEN task_field = 1 THEN 1 END) AS 报告快照数量,
    COUNT(CASE WHEN task_field = 2 THEN 1 END) AS 图文报告数量
FROM t_task 
WHERE is_deleted = 0 
    AND org_code = '12622827439150531N'
    AND task_field IN (1, 2)  -- 报告快照和图文报告
    AND create_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', create_time)
ORDER BY 统计日期 DESC;

-- 1.3 综合下载状态统计（按小时）
-- 功能：按小时统计所有类型数据的下载状态，用于监控实时下载情况
SELECT 
    DATE_TRUNC('hour', create_time) AS 统计小时,
    CASE 
        WHEN task_field = 0 THEN '影像数据'
        WHEN task_field = 1 THEN '报告快照'
        WHEN task_field = 2 THEN '图文报告'
        WHEN task_field = 3 THEN '申请单'
        ELSE '其他'
    END AS 数据类型,
    COUNT(*) AS 任务总数,
    COUNT(CASE WHEN task_status = 2 THEN 1 END) AS 成功数量,
    COUNT(CASE WHEN task_status = 0 THEN 1 END) AS 未下载数量,
    COUNT(CASE WHEN task_status = 1 THEN 1 END) AS 下载中数量
FROM t_task 
WHERE is_deleted = 0 
    AND org_code = '12622827439150531N'
    AND create_time >= CURRENT_DATE  -- 当天数据
GROUP BY DATE_TRUNC('hour', create_time), task_field
ORDER BY 统计小时 DESC, task_field;

/*
================================================================================
二、按检查时间维度统计查询
================================================================================
*/

-- 2.1 按检查日期统计影像数据下载完成情况
-- 功能：按检查发生日期统计影像数据的下载完成情况
-- 业务意义：了解历史检查数据的数字化完成程度
SELECT 
    DATE_TRUNC('day', check_date_time)::DATE AS 检查日期,
    COUNT(*) AS 检查总数,
    COUNT(CASE WHEN task_status = 2 THEN 1 END) AS 已完成下载,
    COUNT(CASE WHEN task_status IN (0, 1) THEN 1 END) AS 待下载,
    ROUND(
        COUNT(CASE WHEN task_status = 2 THEN 1 END) * 100.0 / COUNT(*), 2
    ) AS 完成率_百分比,
    -- 平均下载延迟（检查时间到下载完成时间的间隔）
    ROUND(
        AVG(CASE 
            WHEN task_status = 2 AND update_time IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (update_time - check_date_time)) / 3600.0 
        END), 2
    ) AS 平均下载延迟_小时
FROM t_task 
WHERE is_deleted = 0 
    AND org_code = '12622827439150531N'
    AND task_field = 0  -- 影像数据
    AND check_date_time IS NOT NULL
    AND check_date_time >= CURRENT_DATE - INTERVAL '90 days'  -- 最近90天的检查
GROUP BY DATE_TRUNC('day', check_date_time)
ORDER BY 检查日期 DESC;

-- 2.2 按检查科室统计下载完成情况
-- 功能：分析不同科室的影像数据下载完成情况
SELECT 
    COALESCE(apply_dept_name, '未知科室') AS 申请科室,
    COUNT(*) AS 检查总数,
    COUNT(CASE WHEN task_status = 2 THEN 1 END) AS 已完成下载,
    ROUND(
        COUNT(CASE WHEN task_status = 2 THEN 1 END) * 100.0 / COUNT(*), 2
    ) AS 完成率_百分比,
    COUNT(CASE WHEN task_status = 0 THEN 1 END) AS 未开始下载,
    COUNT(CASE WHEN task_status = 1 THEN 1 END) AS 下载中
FROM t_task 
WHERE is_deleted = 0 
    AND org_code = '12622827439150531N'
    AND check_date_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY apply_dept_name
HAVING COUNT(*) >= 5  -- 只显示检查数量>=5的科室
ORDER BY 完成率_百分比 DESC, 检查总数 DESC;

/*
================================================================================
三、时间范围汇总统计查询
================================================================================
*/

-- 3.1 当天统计汇总
-- 功能：提供当天的全面下载统计概览
SELECT 
    '当天汇总' AS 统计范围,
    CURRENT_DATE AS 统计日期,
    -- 影像数据统计
    COUNT(CASE WHEN task_field = 0 THEN 1 END) AS 影像任务总数,
    COUNT(CASE WHEN task_field = 0 AND task_status = 2 THEN 1 END) AS 影像下载成功,
    -- PDF报告统计  
    COUNT(CASE WHEN task_field IN (1, 2) THEN 1 END) AS 报告任务总数,
    COUNT(CASE WHEN task_field IN (1, 2) AND task_status = 2 THEN 1 END) AS 报告下载成功,
    -- 整体统计
    COUNT(*) AS 总任务数,
    COUNT(CASE WHEN task_status = 2 THEN 1 END) AS 总成功数,
    ROUND(
        COUNT(CASE WHEN task_status = 2 THEN 1 END) * 100.0 / COUNT(*), 2
    ) AS 整体成功率_百分比
FROM t_task 
WHERE is_deleted = 0 
    AND org_code = '12622827439150531N'
    AND create_time >= CURRENT_DATE
    AND create_time < CURRENT_DATE + INTERVAL '1 day';

-- 3.2 本周统计汇总
SELECT 
    '本周汇总' AS 统计范围,
    DATE_TRUNC('week', CURRENT_TIMESTAMP)::DATE AS 周开始日期,
    COUNT(CASE WHEN task_field = 0 THEN 1 END) AS 影像任务总数,
    COUNT(CASE WHEN task_field = 0 AND task_status = 2 THEN 1 END) AS 影像下载成功,
    COUNT(CASE WHEN task_field IN (1, 2) THEN 1 END) AS 报告任务总数,
    COUNT(CASE WHEN task_field IN (1, 2) AND task_status = 2 THEN 1 END) AS 报告下载成功,
    COUNT(*) AS 总任务数,
    COUNT(CASE WHEN task_status = 2 THEN 1 END) AS 总成功数,
    ROUND(
        COUNT(CASE WHEN task_status = 2 THEN 1 END) * 100.0 / COUNT(*), 2
    ) AS 整体成功率_百分比
FROM t_task 
WHERE is_deleted = 0 
    AND org_code = '12622827439150531N'
    AND create_time >= DATE_TRUNC('week', CURRENT_TIMESTAMP);

-- 3.3 本月统计汇总
SELECT 
    '本月汇总' AS 统计范围,
    DATE_TRUNC('month', CURRENT_TIMESTAMP)::DATE AS 月开始日期,
    COUNT(CASE WHEN task_field = 0 THEN 1 END) AS 影像任务总数,
    COUNT(CASE WHEN task_field = 0 AND task_status = 2 THEN 1 END) AS 影像下载成功,
    COUNT(CASE WHEN task_field IN (1, 2) THEN 1 END) AS 报告任务总数,
    COUNT(CASE WHEN task_field IN (1, 2) AND task_status = 2 THEN 1 END) AS 报告下载成功,
    COUNT(*) AS 总任务数,
    COUNT(CASE WHEN task_status = 2 THEN 1 END) AS 总成功数,
    ROUND(
        COUNT(CASE WHEN task_status = 2 THEN 1 END) * 100.0 / COUNT(*), 2
    ) AS 整体成功率_百分比
FROM t_task 
WHERE is_deleted = 0 
    AND org_code = '12622827439150531N'
    AND create_time >= DATE_TRUNC('month', CURRENT_TIMESTAMP);

-- 3.4 本年统计汇总
SELECT 
    '本年汇总' AS 统计范围,
    DATE_TRUNC('year', CURRENT_TIMESTAMP)::DATE AS 年开始日期,
    COUNT(CASE WHEN task_field = 0 THEN 1 END) AS 影像任务总数,
    COUNT(CASE WHEN task_field = 0 AND task_status = 2 THEN 1 END) AS 影像下载成功,
    COUNT(CASE WHEN task_field IN (1, 2) THEN 1 END) AS 报告任务总数,
    COUNT(CASE WHEN task_field IN (1, 2) AND task_status = 2 THEN 1 END) AS 报告下载成功,
    COUNT(*) AS 总任务数,
    COUNT(CASE WHEN task_status = 2 THEN 1 END) AS 总成功数,
    ROUND(
        COUNT(CASE WHEN task_status = 2 THEN 1 END) * 100.0 / COUNT(*), 2
    ) AS 整体成功率_百分比
FROM t_task
WHERE is_deleted = 0
    AND org_code = '12622827439150531N'
    AND create_time >= DATE_TRUNC('year', CURRENT_TIMESTAMP);

/*
================================================================================
四、运维监控扩展查询
================================================================================
*/

-- 4.1 下载失败率趋势分析
-- 功能：分析最近30天的下载失败率趋势，识别异常时间段
-- 结果：包含失败原因分析和趋势变化
WITH daily_stats AS (
    SELECT
        DATE_TRUNC('day', dr.download_begin_time)::DATE AS 统计日期,
        COUNT(*) AS 下载尝试总数,
        COUNT(CASE WHEN dr.download_fail_reason != '' THEN 1 END) AS 失败次数,
        COUNT(CASE WHEN t.task_status = 2 THEN 1 END) AS 成功次数
    FROM t_download_record dr
    LEFT JOIN t_task t ON dr.task_id = t.task_id
    WHERE dr.is_deleted = 0
        AND dr.org_code = '12622827439150531N'
        AND dr.download_begin_time >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY DATE_TRUNC('day', dr.download_begin_time)
)
SELECT
    统计日期,
    下载尝试总数,
    失败次数,
    成功次数,
    ROUND(失败次数 * 100.0 / 下载尝试总数, 2) AS 失败率_百分比,
    CASE
        WHEN 失败次数 * 100.0 / 下载尝试总数 > 20 THEN '异常高'
        WHEN 失败次数 * 100.0 / 下载尝试总数 > 10 THEN '偏高'
        WHEN 失败次数 * 100.0 / 下载尝试总数 > 5 THEN '正常'
        ELSE '良好'
    END AS 失败率评级
FROM daily_stats
WHERE 下载尝试总数 > 0
ORDER BY 统计日期 DESC;

-- 4.2 下载失败原因分析
-- 功能：统计和分析下载失败的主要原因
SELECT
    CASE
        WHEN download_fail_reason LIKE '%超时%' OR download_fail_reason LIKE '%timeout%' THEN '网络超时'
        WHEN download_fail_reason LIKE '%连接%' OR download_fail_reason LIKE '%connection%' THEN '连接异常'
        WHEN download_fail_reason LIKE '%服务器异常%' THEN '服务器异常'
        WHEN download_fail_reason LIKE '%权限%' OR download_fail_reason LIKE '%auth%' THEN '权限问题'
        WHEN download_fail_reason = '' THEN '未知原因'
        ELSE '其他原因'
    END AS 失败原因分类,
    COUNT(*) AS 失败次数,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) AS 占比_百分比,
    -- 显示最新的具体失败原因示例
    MAX(download_fail_reason) AS 具体失败原因示例
FROM t_download_record
WHERE is_deleted = 0
    AND org_code = '12622827439150531N'
    AND download_fail_reason != ''
    AND download_begin_time >= CURRENT_DATE - INTERVAL '7 days'  -- 最近7天
GROUP BY
    CASE
        WHEN download_fail_reason LIKE '%超时%' OR download_fail_reason LIKE '%timeout%' THEN '网络超时'
        WHEN download_fail_reason LIKE '%连接%' OR download_fail_reason LIKE '%connection%' THEN '连接异常'
        WHEN download_fail_reason LIKE '%服务器异常%' THEN '服务器异常'
        WHEN download_fail_reason LIKE '%权限%' OR download_fail_reason LIKE '%auth%' THEN '权限问题'
        WHEN download_fail_reason = '' THEN '未知原因'
        ELSE '其他原因'
    END
ORDER BY 失败次数 DESC;

-- 4.3 下载耗时统计分析
-- 功能：分析下载耗时分布，识别性能瓶颈
SELECT
    CASE
        WHEN task_field = 0 THEN '影像数据'
        WHEN task_field = 1 THEN '报告快照'
        WHEN task_field = 2 THEN '图文报告'
        ELSE '其他'
    END AS 数据类型,
    COUNT(*) AS 下载次数,
    ROUND(AVG(download_spend_time), 2) AS 平均耗时_秒,
    ROUND(MIN(download_spend_time), 2) AS 最短耗时_秒,
    ROUND(MAX(download_spend_time), 2) AS 最长耗时_秒,
    -- 耗时分布统计
    COUNT(CASE WHEN download_spend_time <= 10 THEN 1 END) AS 十秒内完成,
    COUNT(CASE WHEN download_spend_time > 10 AND download_spend_time <= 60 THEN 1 END) AS 一分钟内完成,
    COUNT(CASE WHEN download_spend_time > 60 AND download_spend_time <= 300 THEN 1 END) AS 五分钟内完成,
    COUNT(CASE WHEN download_spend_time > 300 THEN 1 END) AS 超过五分钟,
    -- 性能评级
    CASE
        WHEN AVG(download_spend_time) <= 30 THEN '优秀'
        WHEN AVG(download_spend_time) <= 60 THEN '良好'
        WHEN AVG(download_spend_time) <= 120 THEN '一般'
        ELSE '需优化'
    END AS 性能评级
FROM t_download_record
WHERE is_deleted = 0
    AND org_code = '12622827439150531N'
    AND download_spend_time > 0
    AND download_begin_time >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY task_field
ORDER BY 平均耗时_秒 DESC;

-- 4.4 数据积压情况监控
-- 功能：监控未下载任务的积压情况，按积压时间分析
SELECT
    CASE
        WHEN task_field = 0 THEN '影像数据'
        WHEN task_field = 1 THEN '报告快照'
        WHEN task_field = 2 THEN '图文报告'
        WHEN task_field = 3 THEN '申请单'
        ELSE '其他'
    END AS 数据类型,
    COUNT(*) AS 积压任务数,
    -- 按积压时间分类
    COUNT(CASE WHEN EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - create_time)) / 3600 <= 1 THEN 1 END) AS 一小时内,
    COUNT(CASE WHEN EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - create_time)) / 3600 > 1
               AND EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - create_time)) / 3600 <= 24 THEN 1 END) AS 一天内,
    COUNT(CASE WHEN EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - create_time)) / 3600 > 24
               AND EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - create_time)) / 3600 <= 168 THEN 1 END) AS 一周内,
    COUNT(CASE WHEN EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - create_time)) / 3600 > 168 THEN 1 END) AS 超过一周,
    -- 平均积压时间
    ROUND(AVG(EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - create_time)) / 3600), 2) AS 平均积压时间_小时,
    -- 最长积压时间
    ROUND(MAX(EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - create_time)) / 3600), 2) AS 最长积压时间_小时
FROM t_task
WHERE is_deleted = 0
    AND org_code = '12622827439150531N'
    AND task_status = 0  -- 未下载状态
GROUP BY task_field
ORDER BY 积压任务数 DESC;

-- 4.5 异常数据识别查询
-- 功能：识别长时间处于"下载中"状态的异常任务
SELECT
    task_id AS 任务ID,
    study_id AS 检查号,
    annet_id AS 平台检查标识,
    CASE
        WHEN task_field = 0 THEN '影像数据'
        WHEN task_field = 1 THEN '报告快照'
        WHEN task_field = 2 THEN '图文报告'
        WHEN task_field = 3 THEN '申请单'
        ELSE '其他'
    END AS 数据类型,
    create_time AS 任务创建时间,
    receive_time AS 任务接收时间,
    ROUND(EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - COALESCE(receive_time, create_time))) / 3600, 2) AS 持续时间_小时,
    apply_dept_name AS 申请科室,
    CASE
        WHEN EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - COALESCE(receive_time, create_time))) / 3600 > 24 THEN '严重异常'
        WHEN EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - COALESCE(receive_time, create_time))) / 3600 > 4 THEN '需关注'
        ELSE '正常'
    END AS 异常级别
FROM t_task
WHERE is_deleted = 0
    AND org_code = '12622827439150531N'
    AND task_status = 1  -- 下载中状态
    AND EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - COALESCE(receive_time, create_time))) / 3600 > 1  -- 超过1小时
ORDER BY 持续时间_小时 DESC
LIMIT 50;

-- 4.6 重复失败任务分析
-- 功能：识别多次下载失败的任务，需要人工干预
SELECT
    t.task_id AS 任务ID,
    t.study_id AS 检查号,
    t.annet_id AS 平台检查标识,
    CASE
        WHEN t.task_field = 0 THEN '影像数据'
        WHEN t.task_field = 1 THEN '报告快照'
        WHEN t.task_field = 2 THEN '图文报告'
        WHEN t.task_field = 3 THEN '申请单'
        ELSE '其他'
    END AS 数据类型,
    t.download_fail_count AS 失败次数,
    t.create_time AS 任务创建时间,
    t.apply_dept_name AS 申请科室,
    -- 最近一次失败原因
    (SELECT download_fail_reason
     FROM t_download_record dr
     WHERE dr.task_id = t.task_id
       AND dr.download_fail_reason != ''
     ORDER BY dr.download_begin_time DESC
     LIMIT 1) AS 最近失败原因,
    CASE
        WHEN t.download_fail_count >= 5 THEN '需紧急处理'
        WHEN t.download_fail_count >= 3 THEN '需关注'
        ELSE '正常'
    END AS 处理优先级
FROM t_task t
WHERE t.is_deleted = 0
    AND t.org_code = '12622827439150531N'
    AND t.download_fail_count > 2  -- 失败次数大于2次
ORDER BY t.download_fail_count DESC, t.create_time ASC
LIMIT 100;

-- 4.7 系统负载相关统计
-- 功能：分析系统负载情况，识别高峰时段
WITH hourly_load AS (
    SELECT
        DATE_TRUNC('hour', create_time) AS 统计小时,
        COUNT(*) AS 任务创建数,
        COUNT(CASE WHEN task_status = 1 THEN 1 END) AS 并发下载数,
        COUNT(CASE WHEN task_status = 2 THEN 1 END) AS 完成下载数
    FROM t_task
    WHERE is_deleted = 0
        AND org_code = '12622827439150531N'
        AND create_time >= CURRENT_DATE - INTERVAL '7 days'
    GROUP BY DATE_TRUNC('hour', create_time)
)
SELECT
    统计小时,
    任务创建数,
    并发下载数,
    完成下载数,
    CASE
        WHEN 任务创建数 > 100 THEN '高负载'
        WHEN 任务创建数 > 50 THEN '中负载'
        WHEN 任务创建数 > 20 THEN '低负载'
        ELSE '空闲'
    END AS 负载等级,
    CASE
        WHEN EXTRACT(HOUR FROM 统计小时) BETWEEN 8 AND 18 THEN '工作时间'
        WHEN EXTRACT(HOUR FROM 统计小时) BETWEEN 19 AND 23 THEN '晚间时间'
        ELSE '夜间时间'
    END AS 时间段分类
FROM hourly_load
WHERE 任务创建数 > 0
ORDER BY 统计小时 DESC
LIMIT 168;  -- 最近7天的小时数据

-- 4.8 高峰时段分析
-- 功能：识别下载任务的高峰时段，用于资源调度优化
SELECT
    EXTRACT(HOUR FROM create_time) AS 小时,
    COUNT(*) AS 任务总数,
    ROUND(AVG(COUNT(*)) OVER(), 2) AS 平均任务数,
    CASE
        WHEN COUNT(*) > AVG(COUNT(*)) OVER() * 1.5 THEN '高峰时段'
        WHEN COUNT(*) > AVG(COUNT(*)) OVER() * 1.2 THEN '繁忙时段'
        WHEN COUNT(*) < AVG(COUNT(*)) OVER() * 0.5 THEN '空闲时段'
        ELSE '正常时段'
    END AS 时段特征,
    -- 按数据类型分布
    COUNT(CASE WHEN task_field = 0 THEN 1 END) AS 影像任务数,
    COUNT(CASE WHEN task_field IN (1, 2) THEN 1 END) AS 报告任务数
FROM t_task
WHERE is_deleted = 0
    AND org_code = '12622827439150531N'
    AND create_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY EXTRACT(HOUR FROM create_time)
ORDER BY 小时;

/*
================================================================================
五、性能优化建议和索引使用说明
================================================================================

5.1 现有索引利用情况：
- idx_t_task_is_deleted_task_status_check_date_time_org_code:
  适用于按检查时间和机构筛选的查询
- idx_t_task_is_deleted_task_status_report_date_time_org_code:
  适用于按报告时间和机构筛选的查询
- idx_t_task_create_time: 适用于按创建时间排序的查询
- t_task_annet_id: 适用于按annet_id查找的查询

5.2 查询优化建议：

A. 时间范围查询优化：
   - 使用具体的时间范围而不是开放式查询
   - 优先使用create_time字段进行时间筛选
   - 大数据量统计考虑按月或按周分批处理

B. 分页查询建议：
   - 大结果集查询使用LIMIT和OFFSET
   - 考虑使用游标分页提高性能

C. 聚合查询优化：
   - 复杂统计查询考虑创建物化视图
   - 定期更新统计信息：ANALYZE t_task;

5.3 建议新增索引（如果查询频繁）：
*/

-- 建议索引1：优化按创建时间和任务类型的查询
-- CREATE INDEX CONCURRENTLY idx_t_task_create_time_task_field_org_code
-- ON t_task (create_time, task_field, org_code)
-- WHERE is_deleted = 0;

-- 建议索引2：优化下载记录表的时间查询
-- CREATE INDEX CONCURRENTLY idx_t_download_record_begin_time_org_code
-- ON t_download_record (download_begin_time, org_code)
-- WHERE is_deleted = 0;

-- 建议索引3：优化失败任务查询
-- CREATE INDEX CONCURRENTLY idx_t_task_fail_count_status
-- ON t_task (download_fail_count, task_status, org_code)
-- WHERE is_deleted = 0 AND download_fail_count > 0;

/*
5.4 查询执行计划分析：
使用 EXPLAIN (ANALYZE, BUFFERS) 分析具体查询的执行计划
示例：
EXPLAIN (ANALYZE, BUFFERS)
SELECT COUNT(*) FROM t_task
WHERE org_code = '12622827439150531N'
  AND create_time >= CURRENT_DATE;

5.5 监控建议：
- 定期检查慢查询日志
- 监控数据库连接数和锁等待
- 设置查询超时时间
- 定期更新表统计信息

5.6 数据维护建议：
- 定期清理过期的下载记录（建议保留6个月）
- 监控表空间使用情况
- 定期执行VACUUM和REINDEX操作

================================================================================
使用说明：
1. 所有查询都已针对镇沅县人民医院进行了优化
2. 时间参数可根据实际需要调整
3. 建议在生产环境执行前先在测试环境验证
4. 大数据量查询建议在业务低峰期执行
5. 定期检查和更新本文档中的查询语句

维护记录：
- 2025-07-28: 初始版本创建
- 后续更新请在此处记录修改内容和时间

联系信息：
如有问题或需要扩展功能，请联系系统管理员
================================================================================
*/

/*
================================================================================
六、关联t_study表的扩展查询
================================================================================
*/

-- 6.1 基于t_study表的今天每小时下载情况统计
-- 功能：关联t_task和t_study表，按download_end_time统计今天每小时的下载完成情况
-- 业务意义：从检查信息角度分析实际下载完成的时间分布
SELECT
    EXTRACT(HOUR FROM s.download_end_time) AS 下载完成小时,
    COUNT(*) AS 下载完成总数,
    -- 按检查类型分类统计
    COUNT(CASE WHEN s.check_type = 0 THEN 1 END) AS 放射类,
    COUNT(CASE WHEN s.check_type = 1 THEN 1 END) AS 超声类,
    COUNT(CASE WHEN s.check_type = 2 THEN 1 END) AS 内镜类,
    COUNT(CASE WHEN s.check_type = 3 THEN 1 END) AS 核医学类,
    COUNT(CASE WHEN s.check_type = 4 THEN 1 END) AS 病理类,
    COUNT(CASE WHEN s.check_type >= 5 THEN 1 END) AS 其他类型,
    -- 按设备类型分类
    COUNT(CASE WHEN s.check_modality_type = 'CT' THEN 1 END) AS CT检查,
    COUNT(CASE WHEN s.check_modality_type = 'MR' THEN 1 END) AS MR检查,
    COUNT(CASE WHEN s.check_modality_type = 'DR' THEN 1 END) AS DR检查,
    COUNT(CASE WHEN s.check_modality_type = 'US' THEN 1 END) AS US检查,
    COUNT(CASE WHEN s.check_modality_type NOT IN ('CT','MR','DR','US') THEN 1 END) AS 其他设备,
    -- 平均下载耗时
    ROUND(AVG(s.download_spend_time), 2) AS 平均下载耗时_秒,
    -- 平均文件大小
    ROUND(AVG(s.file_size), 2) AS 平均文件大小_KB,
    -- 下载效率评估
    CASE
        WHEN AVG(s.download_spend_time) <= 30 THEN '高效'
        WHEN AVG(s.download_spend_time) <= 60 THEN '正常'
        WHEN AVG(s.download_spend_time) <= 120 THEN '偏慢'
        ELSE '需优化'
    END AS 下载效率评级
FROM t_study s
INNER JOIN t_task t ON s.annet_id = t.annet_id
WHERE s.is_deleted = 0
    AND s.org_code = '12622827439150531N'
    AND s.download_end_time IS NOT NULL
    AND s.image_status = 1  -- 下载成功
    AND DATE_TRUNC('day', s.download_end_time) = CURRENT_DATE  -- 今天
GROUP BY EXTRACT(HOUR FROM s.download_end_time)
ORDER BY 下载完成小时;

-- 6.2 今天每小时下载情况详细分析（包含失败情况）
-- 功能：全面分析今天每小时的下载尝试和结果情况
SELECT
    EXTRACT(HOUR FROM COALESCE(s.download_end_time, s.download_begin_time)) AS 统计小时,
    COUNT(*) AS 总下载尝试,
    COUNT(CASE WHEN s.image_status = 1 THEN 1 END) AS 成功下载,
    COUNT(CASE WHEN s.image_status = 0 THEN 1 END) AS 失败下载,
    COUNT(CASE WHEN s.image_status = 3 THEN 1 END) AS 下载中,
    COUNT(CASE WHEN s.image_status = 2 THEN 1 END) AS 待下载,
    -- 成功率计算
    ROUND(
        COUNT(CASE WHEN s.image_status = 1 THEN 1 END) * 100.0 /
        NULLIF(COUNT(*), 0), 2
    ) AS 成功率_百分比,
    -- 失败率计算
    ROUND(
        COUNT(CASE WHEN s.image_status = 0 THEN 1 END) * 100.0 /
        NULLIF(COUNT(*), 0), 2
    ) AS 失败率_百分比,
    -- 按科室分布（前3个科室）
    STRING_AGG(
        DISTINCT CASE
            WHEN s.apply_dept_name IS NOT NULL AND s.apply_dept_name != ''
            THEN s.apply_dept_name
        END, ', '
    ) AS 主要申请科室,
    -- 时段特征
    CASE
        WHEN EXTRACT(HOUR FROM COALESCE(s.download_end_time, s.download_begin_time)) BETWEEN 8 AND 12 THEN '上午工作时间'
        WHEN EXTRACT(HOUR FROM COALESCE(s.download_end_time, s.download_begin_time)) BETWEEN 13 AND 18 THEN '下午工作时间'
        WHEN EXTRACT(HOUR FROM COALESCE(s.download_end_time, s.download_begin_time)) BETWEEN 19 AND 23 THEN '晚间时间'
        ELSE '夜间时间'
    END AS 时段分类
FROM t_study s
INNER JOIN t_task t ON s.annet_id = t.annet_id
WHERE s.is_deleted = 0
    AND s.org_code = '12622827439150531N'
    AND (s.download_end_time IS NOT NULL OR s.download_begin_time IS NOT NULL)
    AND (
        DATE_TRUNC('day', s.download_end_time) = CURRENT_DATE OR
        DATE_TRUNC('day', s.download_begin_time) = CURRENT_DATE
    )
GROUP BY EXTRACT(HOUR FROM COALESCE(s.download_end_time, s.download_begin_time))
ORDER BY 统计小时;

-- 6.3 今天下载完成的检查详情（按科室和设备类型）
-- 功能：提供今天下载完成检查的详细分布情况
SELECT
    COALESCE(s.apply_dept_name, '未知科室') AS 申请科室,
    s.check_modality_type AS 设备类型,
    COUNT(*) AS 下载完成数量,
    ROUND(AVG(s.download_spend_time), 2) AS 平均耗时_秒,
    ROUND(AVG(s.file_size), 2) AS 平均文件大小_KB,
    MIN(s.download_end_time) AS 最早完成时间,
    MAX(s.download_end_time) AS 最晚完成时间,
    -- 耗时分布
    COUNT(CASE WHEN s.download_spend_time <= 30 THEN 1 END) AS 30秒内完成,
    COUNT(CASE WHEN s.download_spend_time > 30 AND s.download_spend_time <= 60 THEN 1 END) AS 1分钟内完成,
    COUNT(CASE WHEN s.download_spend_time > 60 THEN 1 END) AS 超过1分钟,
    -- 文件大小分布
    COUNT(CASE WHEN s.file_size <= 10000 THEN 1 END) AS 小于10MB,
    COUNT(CASE WHEN s.file_size > 10000 AND s.file_size <= 50000 THEN 1 END) AS 10MB到50MB,
    COUNT(CASE WHEN s.file_size > 50000 THEN 1 END) AS 大于50MB
FROM t_study s
INNER JOIN t_task t ON s.annet_id = t.annet_id
WHERE s.is_deleted = 0
    AND s.org_code = '12622827439150531N'
    AND s.download_end_time IS NOT NULL
    AND s.image_status = 1  -- 下载成功
    AND DATE_TRUNC('day', s.download_end_time) = CURRENT_DATE
GROUP BY s.apply_dept_name, s.check_modality_type
HAVING COUNT(*) >= 1  -- 至少有1个下载完成
ORDER BY 下载完成数量 DESC, 申请科室, 设备类型;

-- 6.4 今天下载失败的检查分析
-- 功能：分析今天下载失败的检查，帮助定位问题
SELECT
    EXTRACT(HOUR FROM s.download_begin_time) AS 失败发生小时,
    COUNT(*) AS 失败次数,
    COALESCE(s.apply_dept_name, '未知科室') AS 申请科室,
    s.check_modality_type AS 设备类型,
    -- 失败原因分类
    CASE
        WHEN s.download_fail_reason LIKE '%超时%' OR s.download_fail_reason LIKE '%timeout%' THEN '网络超时'
        WHEN s.download_fail_reason LIKE '%连接%' OR s.download_fail_reason LIKE '%connection%' THEN '连接异常'
        WHEN s.download_fail_reason LIKE '%服务器异常%' THEN '服务器异常'
        WHEN s.download_fail_reason LIKE '%权限%' OR s.download_fail_reason LIKE '%auth%' THEN '权限问题'
        WHEN s.download_fail_reason = '' OR s.download_fail_reason IS NULL THEN '未知原因'
        ELSE '其他原因'
    END AS 失败原因分类,
    -- 最新失败原因示例
    MAX(s.download_fail_reason) AS 最新失败原因,
    -- 检查信息
    STRING_AGG(DISTINCT s.study_id, ', ') AS 失败检查号示例,
    -- 失败时间分布
    MIN(s.download_begin_time) AS 最早失败时间,
    MAX(s.download_begin_time) AS 最晚失败时间
FROM t_study s
INNER JOIN t_task t ON s.annet_id = t.annet_id
WHERE s.is_deleted = 0
    AND s.org_code = '12622827439150531N'
    AND s.image_status = 0  -- 下载失败
    AND s.download_begin_time IS NOT NULL
    AND DATE_TRUNC('day', s.download_begin_time) = CURRENT_DATE
GROUP BY
    EXTRACT(HOUR FROM s.download_begin_time),
    s.apply_dept_name,
    s.check_modality_type,
    CASE
        WHEN s.download_fail_reason LIKE '%超时%' OR s.download_fail_reason LIKE '%timeout%' THEN '网络超时'
        WHEN s.download_fail_reason LIKE '%连接%' OR s.download_fail_reason LIKE '%connection%' THEN '连接异常'
        WHEN s.download_fail_reason LIKE '%服务器异常%' THEN '服务器异常'
        WHEN s.download_fail_reason LIKE '%权限%' OR s.download_fail_reason LIKE '%auth%' THEN '权限问题'
        WHEN s.download_fail_reason = '' OR s.download_fail_reason IS NULL THEN '未知原因'
        ELSE '其他原因'
    END
ORDER BY 失败次数 DESC, 失败发生小时;

-- 6.5 今天下载性能对比分析
-- 功能：对比不同时段和设备类型的下载性能
WITH hourly_performance AS (
    SELECT
        EXTRACT(HOUR FROM s.download_end_time) AS 小时,
        s.check_modality_type AS 设备类型,
        COUNT(*) AS 下载次数,
        AVG(s.download_spend_time) AS 平均耗时,
        AVG(s.file_size) AS 平均文件大小,
        AVG(s.file_size / NULLIF(s.download_spend_time, 0)) AS 平均下载速度_KB每秒
    FROM t_study s
    INNER JOIN t_task t ON s.annet_id = t.annet_id
    WHERE s.is_deleted = 0
        AND s.org_code = '12622827439150531N'
        AND s.download_end_time IS NOT NULL
        AND s.image_status = 1
        AND s.download_spend_time > 0
        AND DATE_TRUNC('day', s.download_end_time) = CURRENT_DATE
    GROUP BY EXTRACT(HOUR FROM s.download_end_time), s.check_modality_type
)
SELECT
    小时,
    设备类型,
    下载次数,
    ROUND(平均耗时, 2) AS 平均耗时_秒,
    ROUND(平均文件大小, 2) AS 平均文件大小_KB,
    ROUND(平均下载速度_KB每秒, 2) AS 平均下载速度_KB每秒,
    -- 性能评级
    CASE
        WHEN 平均下载速度_KB每秒 >= 1000 THEN '优秀'
        WHEN 平均下载速度_KB每秒 >= 500 THEN '良好'
        WHEN 平均下载速度_KB每秒 >= 200 THEN '一般'
        ELSE '需优化'
    END AS 性能评级,
    -- 与全天平均的对比
    ROUND(
        (平均下载速度_KB每秒 - AVG(平均下载速度_KB每秒) OVER()) /
        NULLIF(AVG(平均下载速度_KB每秒) OVER(), 0) * 100, 2
    ) AS 相对全天平均_百分比
FROM hourly_performance
WHERE 下载次数 >= 3  -- 至少3次下载才有统计意义
ORDER BY 小时, 设备类型;
